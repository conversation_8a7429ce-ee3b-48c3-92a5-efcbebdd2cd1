import React from 'react';
import schedulesApi from 'shared/utils/api/schedules';
import useReactMutation from 'shared/utils/hooks/useReactMutation';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useUpdateInfinityData from 'shared/utils/hooks/useUpdateInfinityData';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import useOpenConfirm from 'shared/uikit/Confirmation/useOpenConfirm';
import useToast from 'shared/uikit/Toast/useToast';
import { useSetEvents } from '@/shared/hooks/useSetEvents';
import {
  ScheduleEventTypes,
  type CreatableSchedulesEventTypes,
} from '@shared/types/schedules/schedules';
import classes from './api-hook/styles.module.scss';
import getSchedulesSectionsQueryKey from '../utils/getSchedulesSectionsQueryKey';
import useSchedulesEvent from './useSchedulesEvent';

type UseDeleteNotificationProps = {
  schedulesEventType: CreatableSchedulesEventTypes;
  eventId: string;
  eventTitle: string;
  onSuccess: (data: any) => void;
};
const useDeleteScheduleEvent = (
  schedulesEventType: CreatableSchedulesEventTypes,
  scheduleEventId: string
) => {
  const { t } = useTranslation();
  const toast = useToast();
  const { openConfirmDialog } = useOpenConfirm();

  const { mutate } = useReactMutation({
    apiFunc: schedulesApi.deleteEvent,
  });
  const { pastQueryKey, upComingQueryKey, meetingQueryKey } =
    getSchedulesSectionsQueryKey(schedulesEventType);
  const { fetchEvents } = useSetEvents();
  const { remove: removeFromPast } = useUpdateInfinityData(pastQueryKey);
  const { remove: removeFromUpcoming } =
    useUpdateInfinityData(upComingQueryKey);
  const { refetch } = useUpdateInfinityData(meetingQueryKey);
  const { closeHandler } = useSchedulesEvent();

  const deleteConfirmTitles = {
    [ScheduleEventTypes.MEETING]: t('delete_meeting'),
    [ScheduleEventTypes.REMINDER]: t('delete_reminder'),
    [ScheduleEventTypes.TASK]: t('delete_task'),
  };
  const names = {
    [ScheduleEventTypes.MEETING]: t('meeting_s'),
    [ScheduleEventTypes.REMINDER]: t('reminder_s'),
    [ScheduleEventTypes.TASK]: t('task_s'),
  };

  const onDeletionSuccessHandler = () => {
    closeHandler();
    removeFromPast(scheduleEventId);
    removeFromUpcoming(scheduleEventId);
    refetch();
    fetchEvents();
  };

  const deleteHandler = ({
    schedulesEventType: eventType,
    eventId,
    eventTitle,
    onSuccess,
  }: UseDeleteNotificationProps) => {
    openConfirmDialog({
      title: deleteConfirmTitles[eventType],
      message: (
        <Flex className={classes.messageWrap}>
          <Typography>{t('r_y_s_w_r')}</Typography>
          <Typography ml={3} mr={3} font="bold">
            {eventTitle}
          </Typography>
          <Typography>{names[eventType]}</Typography>
        </Flex>
      ),
      confirmButtonText: t('remove'),
      cancelButtonText: t('cancel'),
      isAjaxCall: true,
      apiProps: {
        func: mutate,
        variables: { eventId, schedulesEventType: eventType },
        onSuccess: (data: any) => {
          toast({
            type: 'success',
            icon: 'check-circle',
            title: `${
              eventType.charAt(0) + eventType.slice(1).toLocaleLowerCase()
            } ${t('deleted')}`,
            message: `${eventTitle} ${t('suc_removed_calendar')}`,
          });
          setTimeout(() => onSuccess(data), 750);
        },
      },
    });
  };
  return { deleteHandler, onDeletionSuccessHandler };
};

export default useDeleteScheduleEvent;
