import React, { useState } from 'react';
import CheckBoxGroup from './index';

// Example usage of CheckBoxGroup with simple autocomplete mode
const SimpleAutoCompleteExample: React.FC = () => {
  const [selectedItems, setSelectedItems] = useState([]);

  // Predefined list of options for simple autocomplete
  const predefinedOptions = [
    { value: 'apple', label: 'Apple' },
    { value: 'banana', label: 'Banana' },
    { value: 'cherry', label: 'Cherry' },
    { value: 'date', label: 'Date' },
    { value: 'elderberry', label: 'Elderberry' },
    { value: 'fig', label: 'Fig' },
    { value: 'grape', label: 'Grape' },
    { value: 'honeydew', label: 'Honeydew' },
    { value: 'kiwi', label: 'Kiwi' },
    { value: 'lemon', label: 'Lemon' },
    { value: 'mango', label: 'Mango' },
    { value: 'orange', label: 'Orange' },
    { value: 'papaya', label: '<PERSON>ya' },
    { value: 'quince', label: 'Quince' },
    { value: 'raspberry', label: 'Ra<PERSON><PERSON>' },
    { value: 'strawberry', label: 'Strawberry' },
    { value: 'tangerine', label: 'Tangerine' },
    { value: 'watermelon', label: 'Watermelon' },
  ];

  const handleChange = (newSelectedItems: any[]) => {
    setSelectedItems(newSelectedItems);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '600px' }}>
      <h2>Simple AutoComplete Example</h2>
      <p>
        This example demonstrates the new simple autocomplete functionality.
        Type in the input field to filter fruits from the predefined list.
      </p>
      
      <CheckBoxGroup
        name="fruits-simple"
        value={selectedItems}
        onChange={handleChange}
        options={[]} // Empty options since we're using simple mode
        placeholder="Type to search fruits..."
        alwaysShowAutoCompleteInfo={true}
        asyncAutoCompleteProps={{
          // Enable simple mode
          isSimpleMode: true,
          // Provide predefined options for filtering
          simpleOptions: predefinedOptions,
          // Other props
          name: 'fruits-autocomplete',
          placeholder: 'Search fruits...',
        }}
      />

      <div style={{ marginTop: '20px' }}>
        <h3>Selected Items:</h3>
        <ul>
          {selectedItems.map((item: any) => (
            <li key={item.value}>{item.label}</li>
          ))}
        </ul>
      </div>

      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f5f5f5' }}>
        <h4>How to use:</h4>
        <ol>
          <li>Type in the input field to filter the fruit list</li>
          <li>Click on items in the dropdown to select them</li>
          <li>Selected items will appear as checkboxes below</li>
          <li>Click checkboxes to toggle selection</li>
        </ol>
      </div>
    </div>
  );
};

export default SimpleAutoCompleteExample;
