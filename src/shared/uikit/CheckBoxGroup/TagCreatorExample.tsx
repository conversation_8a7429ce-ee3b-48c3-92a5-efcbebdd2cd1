import React, { useState } from 'react';
import CheckBoxGroup from './index';

// Example usage of CheckBoxGroup with tag creator mode
const TagCreatorExample: React.FC = () => {
  const [selectedTags, setSelectedTags] = useState<any[]>([]);

  const handleChange = (newSelectedTags: any[]) => {
    setSelectedTags(newSelectedTags);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '600px' }}>
      <h2>Tag Creator Example</h2>
      <p>
        This example demonstrates the new tag creator functionality.
        Type any text in the input field and press Enter to create a new tag.
        No dropdown will appear - it's just a simple text input with tag creation.
      </p>
      
      <CheckBoxGroup
        name="tag-creator"
        value={selectedTags}
        onChange={handleChange}
        options={[]} // Empty options since we're using tag creator mode
        placeholder="Type a tag and press Enter..."
        alwaysShowAutoCompleteInfo
        optionsVariant="none"
        asyncAutoCompleteProps={{
          // Enable tag creator mode
          isTagCreatorMode: true,
          name: 'tag-creator-autocomplete',
          placeholder: 'Type a tag and press Enter...',
        }}
      />

      <div style={{ marginTop: '20px' }}>
        <h3>Created Tags:</h3>
        {selectedTags.length === 0 ? (
          <p style={{ color: '#666' }}>No tags created yet. Type in the input above and press Enter.</p>
        ) : (
          <ul>
            {selectedTags.map((tag: any) => (
              <li key={tag.value}>
                <strong>{tag.label}</strong> (value: {tag.value})
              </li>
            ))}
          </ul>
        )}
      </div>

      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f0f9ff', borderRadius: '8px' }}>
        <h4>How to use:</h4>
        <ol>
          <li>Type any text in the input field above</li>
          <li>Press Enter to create a new tag</li>
          <li>The input will clear automatically after creating a tag</li>
          <li>Created tags will appear as checkboxes below the input</li>
          <li>Click on checkboxes to toggle selection/deselection</li>
          <li>No dropdown or suggestions will appear while typing</li>
        </ol>
      </div>

      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f0f0f0', borderRadius: '8px' }}>
        <h4>Key Features:</h4>
        <ul>
          <li>✅ <strong>No dropdown:</strong> No autocomplete suggestions appear</li>
          <li>✅ <strong>Free text input:</strong> Users can type anything</li>
          <li>✅ <strong>Enter to create:</strong> Press Enter to add the typed text as a tag</li>
          <li>✅ <strong>Auto-clear:</strong> Input clears after creating a tag</li>
          <li>✅ <strong>Custom items:</strong> Each tag has both label and value set to the typed text</li>
          <li>✅ <strong>Checkbox integration:</strong> Created tags appear as selectable checkboxes</li>
        </ul>
      </div>

      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#fff3cd', borderRadius: '8px' }}>
        <h4>Implementation:</h4>
        <pre style={{ backgroundColor: '#f8f9fa', padding: '10px', borderRadius: '4px', overflow: 'auto' }}>
{`// Tag Creator Mode Usage
asyncAutoCompleteProps={{
  isTagCreatorMode: true,
  name: 'my-tag-creator',
  placeholder: 'Type a tag and press Enter...',
}}

// The component will:
// 1. Show no dropdown options
// 2. Allow free text input
// 3. Create new items when Enter is pressed
// 4. Clear input after item creation
// 5. Add items to checkbox list below`}
        </pre>
      </div>

      <div style={{ marginTop: '20px' }}>
        <h4>Debug Info:</h4>
        <p>Total tags: {selectedTags.length}</p>
        <details>
          <summary>Raw data (click to expand)</summary>
          <pre style={{ backgroundColor: '#f8f9fa', padding: '10px', borderRadius: '4px', fontSize: '12px' }}>
            {JSON.stringify(selectedTags, null, 2)}
          </pre>
        </details>
      </div>
    </div>
  );
};

export default TagCreatorExample;
