import React, { useState } from 'react';
import CheckBoxGroup from './index';

// Minimal test component for tag creator mode
const TestTagCreator: React.FC = () => {
  const [tags, setTags] = useState<any[]>([]);

  const handleChange = (newTags: any[]) => {
    console.log('Tags changed:', newTags);
    setTags(newTags);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '500px' }}>
      <h2>Test Tag Creator Mode</h2>
      <p>Type any text and press Enter. No dropdown should appear.</p>
      
      <CheckBoxGroup
        name="test-tag-creator"
        value={tags}
        onChange={handleChange}
        options={[]}
        placeholder="Type a tag and press Enter..."
        alwaysShowAutoCompleteInfo
        optionsVariant="none"
        asyncAutoCompleteProps={{
          isTagCreatorMode: true,
          name: 'test-tag-creator-autocomplete',
        }}
      />

      <div style={{ marginTop: '20px' }}>
        <h3>Debug Info:</h3>
        <p>Total tags: {tags.length}</p>
        <p>Tags: {JSON.stringify(tags, null, 2)}</p>
      </div>

      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f0f0f0' }}>
        <h4>Expected behavior:</h4>
        <ol>
          <li>No dropdown should appear while typing</li>
          <li>Type "test" and press Enter → should create a tag with label="test" and value="test"</li>
          <li>Input should clear after pressing Enter</li>
          <li>Created tag should appear as a checkbox below the input</li>
          <li>Click the checkbox to toggle selection</li>
        </ol>
      </div>
    </div>
  );
};

export default TestTagCreator;
