import React, { useState } from 'react';
import CheckBoxGroup from './index';

// Minimal test component for simple autocomplete mode
const TestSimpleMode: React.FC = () => {
  const [selectedItems, setSelectedItems] = useState<any[]>([]);

  // Simple test options
  const testOptions = [
    { value: 'apple', label: 'Apple' },
    { value: 'banana', label: 'Banana' },
    { value: 'cherry', label: 'Cherry' },
    { value: 'date', label: 'Date' },
    { value: 'elderberry', label: 'Elderberry' },
  ];

  const handleChange = (newSelectedItems: any[]) => {
    console.log('Selected items changed:', newSelectedItems);
    setSelectedItems(newSelectedItems);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '500px' }}>
      <h2>Test Simple Mode</h2>
      <p>Type in the input to filter fruits. You should see options appear below.</p>
      
      <CheckBoxGroup
        name="test-simple"
        value={selectedItems}
        onChange={handleChange}
        options={[]}
        placeholder="Type to search fruits..."
        alwaysShowAutoCompleteInfo
        optionsVariant="none"
        asyncAutoCompleteProps={{
          isSimpleMode: true,
          simpleOptions: testOptions,
          name: 'test-autocomplete',
          showDropDownWithoutEnteringAnything: true,
        }}
      />

      <div style={{ marginTop: '20px' }}>
        <h3>Debug Info:</h3>
        <p>Selected items count: {selectedItems.length}</p>
        <p>Selected items: {JSON.stringify(selectedItems, null, 2)}</p>
      </div>

      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f0f0f0' }}>
        <h4>Expected behavior:</h4>
        <ol>
          <li>You should see all fruits listed initially</li>
          <li>Type "app" → should show only "Apple"</li>
          <li>Type "e" → should show "Apple", "Cherry", "Date", "Elderberry"</li>
          <li>Click on any fruit to select it</li>
          <li>Selected fruits should appear as checkboxes below the input</li>
        </ol>
      </div>
    </div>
  );
};

export default TestSimpleMode;
