# Testing Simple AutoComplete Mode

## Test Cases

### 1. Basic Simple Mode Functionality
**Test:** Verify that simple mode filters from predefined options
- Set `isSimpleMode: true`
- Provide `simpleOptions` array
- Type in input field
- Verify filtered results appear

### 2. Backward Compatibility
**Test:** Ensure API mode still works as before
- Set `isSimpleMode: false` or omit the property
- Provide API configuration (url, apiFunc, etc.)
- Verify API calls are made

### 3. Empty Search
**Test:** When input is empty in simple mode
- Clear input field
- Verify all options from `simpleOptions` are shown

### 4. No Matches
**Test:** When search term doesn't match any options
- Type a search term that doesn't match any option
- Verify empty results are shown

### 5. Selection Functionality
**Test:** Selecting items from autocomplete
- Type to filter options
- Click on an option
- Verify item is added to checkbox list below
- Verify input is cleared (if `cleanInputAfterSelect` is true)

### 6. Keyboard Navigation
**Test:** Using keyboard to select items
- Type to filter options
- Use arrow keys to navigate
- Press Enter to select
- Verify item is added to checkbox list

## Manual Testing Steps

1. **Setup Test Environment:**
   ```tsx
   import CheckBoxGroup from 'shared/uikit/CheckBoxGroup';
   
   const testOptions = [
     { value: 'apple', label: 'Apple' },
     { value: 'banana', label: 'Banana' },
     { value: 'cherry', label: 'Cherry' },
   ];
   
   <CheckBoxGroup
     name="test"
     value={selectedItems}
     onChange={setSelectedItems}
     options={[]}
     alwaysShowAutoCompleteInfo={true}
     asyncAutoCompleteProps={{
       isSimpleMode: true,
       simpleOptions: testOptions,
       name: 'test-autocomplete',
     }}
   />
   ```

2. **Test Simple Mode:**
   - Type "app" → Should show "Apple"
   - Type "ban" → Should show "Banana"
   - Type "xyz" → Should show no results
   - Clear input → Should show all options

3. **Test Selection:**
   - Click on "Apple" from dropdown
   - Verify "Apple" appears as checkbox below
   - Verify input is cleared
   - Click "Apple" checkbox to deselect

4. **Test API Mode (Backward Compatibility):**
   ```tsx
   asyncAutoCompleteProps={{
     isSimpleMode: false, // or omit
     // API configuration would go here
     name: 'test-autocomplete',
   }}
   ```

## Expected Results

- ✅ Simple mode filters options locally without API calls
- ✅ API mode continues to work as before
- ✅ Selected items appear as checkboxes
- ✅ Input clears after selection (if configured)
- ✅ Keyboard navigation works
- ✅ No TypeScript errors
- ✅ Backward compatibility maintained

## Performance Considerations

- Simple mode should be faster than API mode (no network requests)
- Filtering should be responsive even with large option lists
- Memory usage should be reasonable for typical use cases

## Edge Cases to Test

1. Empty `simpleOptions` array
2. Very large `simpleOptions` array (1000+ items)
3. Options with special characters
4. Options with identical labels but different values
5. Switching between simple and API mode dynamically
