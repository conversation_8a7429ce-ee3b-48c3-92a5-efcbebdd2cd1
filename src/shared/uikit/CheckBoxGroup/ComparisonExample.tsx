import React, { useState } from 'react';
import CheckBoxGroup from './index';

// Example comparing API mode vs Simple mode vs Tag Creator mode
const ComparisonExample: React.FC = () => {
  const [apiModeItems, setApiModeItems] = useState<any[]>([]);
  const [simpleModeItems, setSimpleModeItems] = useState<any[]>([]);
  const [tagCreatorItems, setTagCreatorItems] = useState<any[]>([]);

  // Predefined list for simple mode
  const predefinedTags = [
    { value: 'react', label: 'React' },
    { value: 'typescript', label: 'TypeScript' },
    { value: 'javascript', label: 'JavaScript' },
    { value: 'nodejs', label: 'Node.js' },
    { value: 'nextjs', label: 'Next.js' },
    { value: 'tailwind', label: 'Tailwind CSS' },
    { value: 'sass', label: 'Sass' },
    { value: 'webpack', label: 'Webpack' },
    { value: 'vite', label: 'Vite' },
    { value: 'eslint', label: 'ESLint' },
    { value: 'prettier', label: 'Prettier' },
    { value: 'jest', label: 'Jest' },
    { value: 'cypress', label: 'Cypress' },
    { value: 'storybook', label: 'Storybook' },
    { value: 'docker', label: 'Docker' },
  ];

  return (
    <div style={{ padding: '20px', maxWidth: '1200px' }}>
      <h2>API Mode vs Simple Mode vs Tag Creator Mode</h2>

      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '30px' }}>
        {/* API Mode Example */}
        <div>
          <h3>API Mode (Original Behavior)</h3>
          <p>
            This uses the original API-based autocomplete functionality.
            It would make API calls to fetch suggestions.
          </p>
          
          <CheckBoxGroup
            name="api-mode-example"
            value={apiModeItems}
            onChange={setApiModeItems}
            options={[]}
            placeholder="Search via API..."
            alwaysShowAutoCompleteInfo={true}
            asyncAutoCompleteProps={{
              // API mode (default behavior)
              isSimpleMode: false,
              name: 'api-autocomplete',
              // In a real scenario, you would provide:
              // url: '/api/search',
              // apiFunc: someApiFunction,
              // normalizer: (data) => data.results,
            }}
          />
          
          <div style={{ marginTop: '10px' }}>
            <strong>Selected:</strong>
            <ul>
              {apiModeItems.map((item: any) => (
                <li key={item.value}>{item.label}</li>
              ))}
            </ul>
          </div>
        </div>

        {/* Simple Mode Example */}
        <div>
          <h3>Simple Mode (New Feature)</h3>
          <p>
            This uses the new simple autocomplete mode that filters
            from a predefined list without API calls.
          </p>
          
          <CheckBoxGroup
            name="simple-mode-example"
            value={simpleModeItems}
            onChange={setSimpleModeItems}
            options={[]}
            placeholder="Search technologies..."
            alwaysShowAutoCompleteInfo={true}
            asyncAutoCompleteProps={{
              // Simple mode (new feature)
              isSimpleMode: true,
              simpleOptions: predefinedTags,
              name: 'simple-autocomplete',
            }}
          />
          
          <div style={{ marginTop: '10px' }}>
            <strong>Selected:</strong>
            <ul>
              {simpleModeItems.map((item: any) => (
                <li key={item.value}>{item.label}</li>
              ))}
            </ul>
          </div>
        </div>

        {/* Tag Creator Mode Example */}
        <div>
          <h3>Tag Creator Mode (New Feature)</h3>
          <p>
            This uses the new tag creator mode where users can type any text
            and press Enter to create custom tags without any dropdown.
          </p>

          <CheckBoxGroup
            name="tag-creator-mode-example"
            value={tagCreatorItems}
            onChange={setTagCreatorItems}
            options={[]}
            placeholder="Type a tag and press Enter..."
            alwaysShowAutoCompleteInfo={true}
            asyncAutoCompleteProps={{
              // Tag creator mode (new feature)
              isTagCreatorMode: true,
              name: 'tag-creator-autocomplete',
            }}
          />

          <div style={{ marginTop: '10px' }}>
            <strong>Selected:</strong>
            <ul>
              {tagCreatorItems.map((item: any) => (
                <li key={item.value}>{item.label}</li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      <div style={{ marginTop: '40px', padding: '15px', backgroundColor: '#e8f4fd', borderRadius: '8px' }}>
        <h4>Key Differences:</h4>
        <ul>
          <li><strong>API Mode:</strong> Makes HTTP requests to fetch suggestions (original behavior)</li>
          <li><strong>Simple Mode:</strong> Filters from a predefined list locally with dropdown suggestions</li>
          <li><strong>Tag Creator Mode:</strong> Free text input with no dropdown - press Enter to create custom tags</li>
          <li><strong>Performance:</strong> Simple and Tag Creator modes are faster (no network requests)</li>
          <li><strong>Use Cases:</strong>
            <ul>
              <li>API Mode: Dynamic data from server</li>
              <li>Simple Mode: Known, finite lists with autocomplete</li>
              <li>Tag Creator Mode: User-generated tags, labels, or custom entries</li>
            </ul>
          </li>
        </ul>
      </div>

      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f0f9ff', borderRadius: '8px' }}>
        <h4>Implementation:</h4>
        <pre style={{ backgroundColor: '#f8f9fa', padding: '10px', borderRadius: '4px', overflow: 'auto' }}>
{`// Simple Mode Usage (with dropdown suggestions)
asyncAutoCompleteProps={{
  isSimpleMode: true,
  simpleOptions: [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    // ... more options
  ],
  name: 'my-autocomplete',
}}

// Tag Creator Mode Usage (no dropdown, free text)
asyncAutoCompleteProps={{
  isTagCreatorMode: true,
  name: 'my-tag-creator',
  placeholder: 'Type and press Enter...',
}}

// API Mode Usage (unchanged)
asyncAutoCompleteProps={{
  url: '/api/search',
  apiFunc: myApiFunction,
  name: 'my-autocomplete',
}}`}
        </pre>
      </div>
    </div>
  );
};

export default ComparisonExample;
