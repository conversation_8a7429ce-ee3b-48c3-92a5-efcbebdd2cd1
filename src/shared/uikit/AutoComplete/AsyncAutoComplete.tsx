import React, { useEffect, useRef, useState } from 'react';
import { useDebounceState } from 'shared/utils/hooks/useDebounceState';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import type { AsyncAutoCompleteProps } from 'shared/types/components/Form.type';
import AutoComplete from './index';
import useMedia from '../utils/useMedia';
import fuseSearch from '../utils/fuseSearch';

const AsyncAutoComplete = ({
  name,
  value: parentValue,
  url,
  normalizer,
  onChange: parentOnchange,
  onChangeInput: parentOnChangeInput,
  keywords = 'text',
  isMulti,
  apiFunc,
  initSearchValue = 'a',
  params = {},
  showDropDownWithoutEnteringAnything,
  onOptions,
  optionsVariant,
  hardRefetch = false,
  accessToken,
  isSimpleMode = false,
  simpleOptions = [],
  ...rest
}: AsyncAutoCompleteProps) => {
  const { isMoreThanTablet } = useMedia();
  const [filteredOption, setFilteredOption] = useState<any[]>([]);
  const { debounceValue, setValue } = useDebounceState<string>(
    initSearchValue,
    500
  );
  const ref = useRef<any>(null);

  // Simple mode search functionality
  const search = isSimpleMode
    ? fuseSearch(simpleOptions, { keys: ['label'] })
    : null;

  const onSuccess = (data: any) => {
    const newItems = normalizer ? normalizer(data) : data;
    setFilteredOption(newItems);
    onOptions?.(newItems || []);
  };

  const { refetch } = useReactQuery({
    action: {
      spreadParams: !!apiFunc,
      apiFunc,
      key: [name, debounceValue],
      url,
      params: { [keywords]: debounceValue, ...params },
      accessToken,
    },
    config: {
      enabled: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      staleTime: 60 * 60 * 1000,
      onSuccess,
    },
  });

  // Initialize simple mode options on mount
  useEffect(() => {
    if (isSimpleMode && simpleOptions.length > 0) {
      setFilteredOption(simpleOptions);
      onOptions?.(simpleOptions);
    }
  }, [isSimpleMode, simpleOptions, onOptions]);

  useEffect(() => {
    if (isSimpleMode) {
      // Handle simple mode filtering
      if (search) {
        const filteredItems = debounceValue
          ? search(debounceValue)
          : simpleOptions;
        setFilteredOption(filteredItems);
        onOptions?.(filteredItems || []);
      }
    } else if (
      debounceValue ||
      showDropDownWithoutEnteringAnything ||
      hardRefetch
    ) {
      // Handle API mode
      refetch();
    }
  }, [
    debounceValue,
    refetch,
    showDropDownWithoutEnteringAnything,
    hardRefetch,
    isSimpleMode,
    search,
    simpleOptions,
    onOptions,
  ]);

  const onChangeInput = (input: string) => {
    parentOnChangeInput?.(input);
    setValue(input);
    if (!isMulti) {
      parentOnchange?.({ label: input, value: null });
    }
    if (!input) {
      setValue(initSearchValue);
    }
  };

  const onSelect = (input: string) => parentOnchange?.(input);

  return (
    <AutoComplete
      onChangeInput={onChangeInput}
      onSelect={onSelect}
      ref={ref}
      value={parentValue}
      displayName={parentValue?.label}
      isMulti={isMulti}
      name={name}
      options={
        isSimpleMode
          ? filteredOption
          : debounceValue || showDropDownWithoutEnteringAnything
            ? filteredOption
            : []
      }
      optionsVariant={
        optionsVariant || (isMoreThanTablet ? 'dropdown' : 'modal')
      }
      isShow={isSimpleMode ? filteredOption.length > 0 : undefined}
      {...rest}
    />
  );
};

export default AsyncAutoComplete;
