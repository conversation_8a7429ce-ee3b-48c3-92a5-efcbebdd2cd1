import React, { useEffect, useRef, useState } from 'react';
import { useDebounceState } from 'shared/utils/hooks/useDebounceState';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import type { AsyncAutoCompleteProps } from 'shared/types/components/Form.type';
import AutoComplete from './index';
import useMedia from '../utils/useMedia';
import fuseSearch from '../utils/fuseSearch';

const AsyncAutoComplete = ({
  name,
  value: parentValue,
  url,
  normalizer,
  onChange: parentOnchange,
  onChangeInput: parentOnChangeInput,
  keywords = 'text',
  isMulti,
  apiFunc,
  initSearchValue = 'a',
  params = {},
  showDropDownWithoutEnteringAnything,
  onOptions,
  optionsVariant,
  hardRefetch = false,
  accessToken,
  isSimpleMode = false,
  simpleOptions = [],
  isTagCreatorMode = false,
  ...rest
}: AsyncAutoCompleteProps) => {
  const { isMoreThanTablet } = useMedia();
  const [filteredOption, setFilteredOption] = useState<any[]>([]);
  const { debounceValue, setValue } = useDebounceState<string>(
    initSearchValue,
    500
  );
  const ref = useRef<any>(null);

  // Simple mode search functionality
  const search = isSimpleMode
    ? fuseSearch(simpleOptions, { keys: ['label'] })
    : null;

  const onSuccess = (data: any) => {
    const newItems = normalizer ? normalizer(data) : data;
    setFilteredOption(newItems);
    onOptions?.(newItems || []);
  };

  const { refetch } = useReactQuery({
    action: {
      spreadParams: !!apiFunc,
      apiFunc,
      key: [name, debounceValue],
      url,
      params: { [keywords]: debounceValue, ...params },
      accessToken,
    },
    config: {
      enabled: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      staleTime: 60 * 60 * 1000,
      onSuccess,
    },
  });

  // Initialize simple mode options on mount
  useEffect(() => {
    if (isSimpleMode && !isTagCreatorMode && simpleOptions.length > 0) {
      setFilteredOption(simpleOptions);
      onOptions?.(simpleOptions);
    }
  }, [isSimpleMode, isTagCreatorMode, simpleOptions, onOptions]);

  useEffect(() => {
    if (isTagCreatorMode) {
      // Tag creator mode: no options, no dropdown
      setFilteredOption([]);
      onOptions?.([]);
    } else if (isSimpleMode) {
      // Handle simple mode filtering
      if (search) {
        const filteredItems = debounceValue
          ? search(debounceValue)
          : simpleOptions;
        setFilteredOption(filteredItems);
        onOptions?.(filteredItems || []);
      }
    } else if (
      debounceValue ||
      showDropDownWithoutEnteringAnything ||
      hardRefetch
    ) {
      // Handle API mode
      refetch();
    }
  }, [
    debounceValue,
    refetch,
    showDropDownWithoutEnteringAnything,
    hardRefetch,
    isSimpleMode,
    isTagCreatorMode,
    search,
    simpleOptions,
    onOptions,
  ]);

  const onChangeInput = (input: string) => {
    parentOnChangeInput?.(input);
    setValue(input);
    if (!isMulti && !isTagCreatorMode) {
      parentOnchange?.({ label: input, value: null });
    }
    if (!input && !isTagCreatorMode) {
      setValue(initSearchValue);
    }
  };

  const onSelect = (input: string) => {
    if (isTagCreatorMode) {
      // In tag creator mode, create a new item from the input text
      const newItem = { label: input, value: input };
      parentOnchange?.(newItem);
    } else {
      parentOnchange?.(input);
    }
  };

  // Handle Enter key in tag creator mode
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (isTagCreatorMode && e.key === 'Enter') {
      e.preventDefault();
      const inputValue = (e.target as HTMLInputElement).value.trim();
      if (inputValue) {
        const newItem = { label: inputValue, value: inputValue };
        parentOnchange?.(newItem);
        // Clear the input after adding
        setValue('');
      }
    }
  };

  return (
    <AutoComplete
      onChangeInput={onChangeInput}
      onSelect={onSelect}
      ref={ref}
      value={parentValue}
      displayName={parentValue?.label}
      isMulti={isMulti}
      name={name}
      options={
        isTagCreatorMode
          ? [] // No options in tag creator mode
          : isSimpleMode
            ? filteredOption
            : debounceValue || showDropDownWithoutEnteringAnything
              ? filteredOption
              : []
      }
      optionsVariant={
        optionsVariant || (isMoreThanTablet ? 'dropdown' : 'modal')
      }
      isShow={
        isTagCreatorMode
          ? false // Never show dropdown in tag creator mode
          : isSimpleMode
            ? filteredOption.length > 0
            : undefined
      }
      textInputProps={{
        ...rest.textInputProps,
        onKeyDown: isTagCreatorMode
          ? handleKeyDown
          : rest.textInputProps?.onKeyDown,
      }}
      {...rest}
    />
  );
};

export default AsyncAutoComplete;
