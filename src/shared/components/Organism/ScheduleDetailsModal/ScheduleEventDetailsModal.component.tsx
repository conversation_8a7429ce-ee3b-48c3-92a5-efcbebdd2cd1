import { useCallback } from 'react';
import { motion } from 'framer-motion';
import cnj from 'shared/uikit/utils/cnj';
import Spinner from 'shared/uikit/Spinner';
import ModalBody from 'shared/uikit/Modal/ModalBody';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import FixedRightSideModalDialog from 'shared/uikit/Modal/FixedRightSideModalDialog';
import { schedulesEventTypes } from 'shared/utils/constants/enums';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useUpdateInfinityData from 'shared/utils/hooks/useUpdateInfinityData';
import useSchedulesEvent from 'shared/hooks/useSchedulesEvent';
import { useSchedulesUrlState } from '@shared/hooks/useSchedulesUrlState';
import getSchedulesSectionsQueryKey from 'shared/utils/getSchedulesSectionsQueryKey';
import { TOP_BAR_NOTIFICATION_BUTTON } from 'shared/constants/enums';
import type { MeetingDetails } from 'shared/types/schedules/schedules';
import { useAuthState } from '@shared/contexts/Auth/auth.provider';
import ScheduleDetailsModalContent from './ScheduleEventDetailsModal.content';
import CelebrationForm from './partials/CelebrationForm';
import classes from './ScheduleEventDetailsModal.component.module.scss';
import ScheduleDetailsModalFooter from './ScheduleEventDetailsModal.footer';
import EventNotFound from './partials/EventNotFound';
import AttendeesViewPanel from '../../../uikit/AttendeesViewPanel/AttendeesViewPanel';
import SchedulesEventDetailsSkeleton from './partials/SchedulesEventDetailsSkeleton';

const ScheduleEventDetailsModal = () => {
  const { t } = useTranslation();
  const isLoggedIn = useAuthState('isLoggedIn');
  const { schedulesEventType, closeHandler, queryResult, event } =
    useSchedulesEvent();
  const { upComingQueryKey } = getSchedulesSectionsQueryKey(schedulesEventType);
  useUpdateInfinityData(upComingQueryKey);

  const {
    state: { scheduleEventsPanelData },
    setScheduleEventsPanelData,
  } = useSchedulesUrlState();
  const { isFromNotification, isInAttendeesView } =
    scheduleEventsPanelData || {};

  const noCloseButton = isFromNotification;

  const titles = {
    [schedulesEventTypes.MEETING]: t('meeting_details'),
    [schedulesEventTypes.REMINDER]: t('reminder_details'),
    [schedulesEventTypes.TASK]: t('task_details'),
    [schedulesEventTypes.HOLIDAY]: t('event_details'),
    [schedulesEventTypes.BIRTHDAY]: t('birthday_details'),
  };

  const isHolidayOrBirthday =
    event?.schedulesEventType === schedulesEventTypes.HOLIDAY ||
    event?.schedulesEventType === schedulesEventTypes.BIRTHDAY;

  const onBack = () => {
    if (!isFromNotification) return;
    closeHandler();
    document?.getElementById(TOP_BAR_NOTIFICATION_BUTTON)?.click?.();
  };

  const backFromAttendees = useCallback(
    () =>
      setScheduleEventsPanelData({
        ...scheduleEventsPanelData,
        isInAttendeesView: false,
      }),
    [setScheduleEventsPanelData, scheduleEventsPanelData]
  );

  return (
    <FixedRightSideModalDialog
      onBack={closeHandler}
      onClose={closeHandler}
      fullBackdrop={!isLoggedIn}
    >
      <ModalHeaderSimple
        closeButtonProps={{ onClick: closeHandler }}
        visibleHeaderDivider
        title={titles[schedulesEventType]}
        hideBack={!noCloseButton}
        backButtonProps={{
          onClick: closeHandler,
        }}
        noCloseButton={noCloseButton}
      />
      {queryResult?.isLoading ? (
        <SchedulesEventDetailsSkeleton />
      ) : (
        <>
          <ModalBody
            className={cnj(
              classes.modalBody,
              isHolidayOrBirthday && classes.holidayBody
            )}
          >
            <motion.div
              className={classes.motion}
              initial="visible"
              animate="hidden"
              variants={{
                visible: { originX: 0, opacity: 0, scale: 0.95 },
                hidden: { originX: 0.5, opacity: 1, scale: 1 },
              }}
              transition={{ duration: 0.2 }}
            >
              {!event ? (
                <EventNotFound eventType={schedulesEventType} />
              ) : (
                <ScheduleDetailsModalContent />
              )}
            </motion.div>
            {isInAttendeesView && (
              <AttendeesViewPanel
                attendees={(event as MeetingDetails).attendees}
                onBack={backFromAttendees}
              />
            )}
          </ModalBody>
          {isHolidayOrBirthday && (
            <CelebrationForm
              closeHandler={closeHandler}
              event={event}
              schedulesEventType={schedulesEventType}
            />
          )}
          <ScheduleDetailsModalFooter
            event={event!}
            className={classes.footer}
          />
        </>
      )}
    </FixedRightSideModalDialog>
  );
};

export default ScheduleEventDetailsModal;
