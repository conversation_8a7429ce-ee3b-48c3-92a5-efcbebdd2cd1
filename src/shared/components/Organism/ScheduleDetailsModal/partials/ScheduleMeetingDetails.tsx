import { useMemo } from 'react';
import cnj from 'classnames';
import Flex from 'shared/uikit/Flex';
import { Time } from 'shared/utils/Time';
import type { MeetingDetails } from 'shared/types/schedules/schedules';
import { ContactType } from 'shared/types/schedules/schedules';
import { provderIconNameMapper } from 'shared/components/molecules/EventsIntegration/Partials/ProviderIcon';
import { ProviderType } from 'shared/components/molecules/EventsIntegration/utils/type';
import Info from 'shared/components/molecules/Info/Info';
import DividerVertical2 from 'shared/uikit/Divider/DividerVertical2';
import { useSchedulesUrlState } from '@shared/hooks/useSchedulesUrlState';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { schedulesDb } from 'shared/utils/constants';
import getEventPermission, {
  canEdit,
} from '@shared/utils/eventPermissions.utils';
import Text from '@shared/components/molecules/Text/Text';
import CustomLink from '@shared/svg/CustomLink';
import { PrivateAttachmentsList } from '@shared/components/molecules/Attachments';
import Typography from '@shared/uikit/Typography';
import Collaborators from './Collaborators';
import ScheduleDetailsCreatorCard from './ScheduleDetailsCreatorCard';
import classes from './ScheduleMeetingDetails.module.scss';

const ScheduleMeetingDetails = ({ meeting }: { meeting: MeetingDetails }) => {
  const { t } = useTranslation();
  const { state, setScheduleEventsPanelData } = useSchedulesUrlState();

  const isRemote = meeting?.contactType?.value === ContactType.REMOTE;

  const [date, time] = useMemo(() => {
    const startTime = Time.getLocalDateFromUTCStr(meeting.start);
    const end = startTime.clone().add(meeting.duration.amount, 'millisecond');
    return [
      startTime.format('LL'),
      `${startTime.format('LT')} - ${end.format('LT')}`,
    ];
  }, [meeting.start, meeting.duration]);

  const attendees = [meeting.creator, ...meeting.attendees];

  const onSeeAllClick = () => {
    setScheduleEventsPanelData({
      ...state.scheduleEventsPanelData,
      isInAttendeesView: true,
    });
  };

  // const EventAttendeePermissions = meeting.attendees[0]?.permissions.map(
  //   (permission) =>
  //     attendeePermissionsOptions.find((apo) => apo.value === permission)!
  // );

  const permissions = getEventPermission(meeting);

  return (
    <Flex className={classes.meetinDetailsWrapper}>
      <ScheduleDetailsCreatorCard event={meeting} />
      <Text label={t('meeting_title')} value={meeting.title || ''} />
      <Text
        label={t('time_zone')}
        value={meeting?.timezone?.label}
        hideIfEmpty
      />
      <Flex flexDir="row" className={cnj(classes.defaultMarginTop)}>
        <Text
          label={t('date')}
          value={date}
          className={classes.dateTimeItemWrapper}
        />
        <DividerVertical2 mx="12px" />
        <Text
          label={t('time')}
          value={time}
          className={classes.dateTimeItemWrapper}
        />
      </Flex>
      {isRemote ? (
        meeting.meetingLink ? (
          (() => {
            const Logo = meeting.externalConferenceProvider?.type
              ? provderIconNameMapper[ProviderType.Conference][
                  meeting.externalConferenceProvider?.type
                ]
              : CustomLink;
            return (
              <Text
                typographyProps={{
                  as: 'a',
                  href: meeting.meetingLink,
                  target: '_blank',
                }}
                label={t('meeting_channel')}
                value={meeting.meetingLink}
                copyText={meeting.meetingLink}
                append={
                  <div
                    className={cnj(
                      classes.logoContainer,
                      'flex justify-center items-center'
                    )}
                  >
                    {Logo && <Logo width="unset" height="unset" />}
                  </div>
                }
              />
            );
          })()
        ) : (
          <Info text={t('meeting_link_15_minutes')} />
        )
      ) : (
        <>
          {meeting.location?.[0]?.location && (
            <Text
              label={t('location')}
              value={meeting.location[0]?.location?.label}
              typographyProps={{
                as: 'a',
                href: `https://maps.google.com?q=${meeting.location[0].location.lat},${meeting.location[0].location.lon}`,
                target: '_blank',
              }}
              copyText={`https://maps.google.com?q="${meeting.location[0].location.lat}","${meeting.location[0].location.lon}"`}
            />
          )}
          <Text
            label={t('address_details')}
            value={meeting?.locationDetails}
            hideIfEmpty
          />
        </>
      )}
      <Text
        label={t('meeting_duration')}
        value={
          schedulesDb.meetingDurationOptions.find(
            (item) => item.value === meeting.duration.value
          )?.shortLabel!
        }
      />
      <Text label={t('meeting_reminder')} value={meeting?.remind?.label} />
      <Text
        label={t('meeting_type')}
        value={meeting?.contactType?.label}
        hideIfEmpty={!canEdit(permissions)}
      />
      <Text
        label={t('description')}
        value={meeting?.description}
        isRichText
        hideIfEmpty={!canEdit(permissions)}
      />
      {meeting.permissions.SEE_OTHER_ATTENDEES && (
        <>
          <Collaborators
            labelKey="attendee"
            value={attendees}
            onSeeAllClick={onSeeAllClick}
            className="pb-20"
          />
        </>
      )}
      {/* {meeting.permissions.SEE_OTHER_ATTENDEES &&
        meeting.attendees.length > 0 && (
          <div>
            <Typography
              color="colorIconForth2"
              font="500"
              size={13}
              height={15}
            >
              {t('attendee_permissions')}
            </Typography>
            <CheckBoxGroup
              classNames={{ container: classes.smallMargin }}
              name="attendeePermissions"
              onChange={() => {}}
              value={EventAttendeePermissions}
              options={attendeePermissionsOptions}
              disabledReadOnly
            />
          </div>
        )} */}
      {Boolean(meeting?.attachmentFileIds?.length) && (
        <>
          <Typography
            color="colorIconForth2"
            font="500"
            size={13}
            height={15}
            mb={12}
          >
            {t('attachments')}
          </Typography>
          <PrivateAttachmentsList
            key={'test key'}
            ids={(meeting?.attachmentFileIds || []).map(Number)}
            className={classes.attachmentsContainer}
          />
        </>
      )}
    </Flex>
  );
};

export default ScheduleMeetingDetails;
