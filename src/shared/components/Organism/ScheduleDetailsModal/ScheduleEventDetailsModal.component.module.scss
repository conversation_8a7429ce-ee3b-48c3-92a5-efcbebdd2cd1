@import '/src/shared/theme/theme.scss';

@layer organism {
  .footer {
    flex-direction: row;
    border-top: 1px solid colors(techGray_10);
  }
  .divider {
    min-width: variables(gutter) * 0.5;
    width: variables(gutter) * 0.5;
    max-width: variables(gutter) * 0.5;
  }

  .holidayBody {
    flex: unset;
    padding-bottom: 0;
  }
  .motion {
    display: flex;
    min-height: 100%;
    & > div {
      width: 100%;
    }
  }

  @media (min-width: breakpoints(tablet)) {
    .footer {
      padding: variables(largeGutter);
    }
    .largeModalHeader {
      height: variables(headerDesktopHeight);
      min-height: variables(headerDesktopHeight);
    }
  }
}
