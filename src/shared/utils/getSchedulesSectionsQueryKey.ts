import type { ScheduleEventTypes } from 'shared/types/schedules/schedules';
import QueryKeys from 'shared/utils/constants/queryKeys';

const getSchedulesSectionsQueryKey = (
  schedulesEventType: ScheduleEventTypes
) => {
  const upComingQueryKey = [QueryKeys.getUpcomingMeetings, schedulesEventType];
  const pastQueryKey = [QueryKeys.getPastMeetings, schedulesEventType];
  const meetingQueryKey = [QueryKeys.candidateMeetings];
  return { pastQueryKey, upComingQueryKey, meetingQueryKey };
};

export default getSchedulesSectionsQueryKey;
